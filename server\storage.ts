import { type User, type InsertUser, type SatelliteMessage, type InsertSatelliteMessage } from "@shared/schema";

export interface IStorage {
  // User management
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Satellite message management
  addSatelliteMessage(message: InsertSatelliteMessage): Promise<SatelliteMessage>;
  getSatelliteMessages(imeis: string[], startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]>;
  getSatelliteMessagesByImei(imei: string, startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]>;
  
  // Device status tracking
  getOnlineDevices(): Promise<string[]>;
  setDeviceOnline(imei: string): Promise<void>;
  setDeviceOffline(imei: string): Promise<SatelliteMessage[]>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private userIdCounter: number;
  private messages: Map<number, SatelliteMessage>;
  private messageIdCounter: number;
  private onlineDevices: Set<string>;

  constructor() {
    this.users = new Map();
    this.userIdCounter = 1;
    this.messages = new Map();
    this.messageIdCounter = 1;
    this.onlineDevices = new Set();
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const user: User = { ...insertUser, id, role: insertUser.role || 'user' };
    this.users.set(id, user);
    return user;
  }

  async addSatelliteMessage(insertMessage: InsertSatelliteMessage): Promise<SatelliteMessage> {
    const id = this.messageIdCounter++;

    const message: SatelliteMessage = {
      ...insertMessage,
      id,
      serverTimestamp: new Date()
    };

    this.messages.set(id, message);
    return message;
  }

  async getSatelliteMessages(imeis: string[], startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]> {
    const messages = Array.from(this.messages.values());
    const filtered = messages.filter(message => {
      if (!imeis.includes(message.imei)) return false;

      // Conversione robusta
      if (typeof message.satelliteTimestamp === 'string') {
        message.satelliteTimestamp = new Date(message.satelliteTimestamp);
      }

      if (startDate && message.satelliteTimestamp < startDate) return false;
      if (endDate && message.satelliteTimestamp > endDate) return false;

      return true;
    }).sort((a, b) => {
      // Conversione robusta anche qui
      const aTime = typeof a.satelliteTimestamp === 'string' ? new Date(a.satelliteTimestamp) : a.satelliteTimestamp;
      const bTime = typeof b.satelliteTimestamp === 'string' ? new Date(b.satelliteTimestamp) : b.satelliteTimestamp;
      return bTime.getTime() - aTime.getTime();
    });
    return filtered;
  }

  async getSatelliteMessagesByImei(imei: string, startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]> {
    return this.getSatelliteMessages([imei], startDate, endDate);
  }

  async getOnlineDevices(): Promise<string[]> {
    return Array.from(this.onlineDevices);
  }

  async setDeviceOnline(imei: string): Promise<void> {
    this.onlineDevices.add(imei);
  }

  async setDeviceOffline(imei: string): Promise<SatelliteMessage[]> {
    this.onlineDevices.delete(imei);
    return []; // No longer processing retroactive buffers
  }
}

export const storage = new MemStorage();

